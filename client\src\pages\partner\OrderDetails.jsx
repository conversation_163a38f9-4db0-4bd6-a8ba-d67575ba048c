import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import api from '../../lib/axios';
import { useToast } from '@/components/ui/use-toast';
import {
    Card,
    CardHeader,
    CardTitle,
    CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, ArrowLeft, RefreshCw, Copy, CheckCircle, CirclePlus, Clock, CreditCard, Smartphone, Wifi, QrCode, Phone, Shield, Globe, Calendar, Hash, Package, Download } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";

const OrderDetails = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const { toast } = useToast();
    const [order, setOrder] = useState(null);
    const [loading, setLoading] = useState(true);
    const [usageData, setUsageData] = useState(null);
    const [loadingUsage, setLoadingUsage] = useState(false);

    const copyToClipboard = async (text, label) => {
        try {
            await navigator.clipboard.writeText(text);
            toast({
                title: "Copied!",
                description: `${label} copied to clipboard`,
            });
        } catch (err) {
            toast({
                variant: "destructive",
                title: "Failed to copy",
                description: "Could not copy to clipboard",
            });
        }
    };

    const getStatusColor = (status) => {
        switch (status?.toLowerCase()) {
            case 'completed':
            case 'activated':
                return 'bg-green-100 text-green-800 border-green-200';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800 border-yellow-200';
            case 'failed':
            case 'expired':
                return 'bg-red-100 text-red-800 border-red-200';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    const downloadQRCode = async (qrCodeUrl, orderId) => {
        try {
            const response = await fetch(qrCodeUrl);
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `eSIM-QR-Code-Order-${orderId}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            toast({
                title: "Downloaded!",
                description: "QR Code has been downloaded successfully",
            });
        } catch (err) {
            toast({
                variant: "destructive",
                title: "Download failed",
                description: "Could not download QR code",
            });
        }
    };

    const fetchUsageData = async () => {
        try {
            setLoadingUsage(true);
            // console.log('Fetching usage data for order:', id);
            const response = await api.get(`/api/orders/${id}/usage`);
            // console.log('Usage data response:', response.data);
            
            // Validate the response data structure
            if (response.data && typeof response.data === 'object') {
                // console.log('Setting usage data:', response.data);
                setUsageData(response.data);
            } else {
                console.error('Invalid usage data format:', response.data);
                toast({
                    variant: "destructive",
                    title: "Error",
                    description: "Invalid usage data format received"
                });
            }
        } catch (error) {
            console.error('Error fetching usage data:', error);
            console.error('Error details:', error.response?.data);
            toast({
                variant: "destructive",
                title: "Error",
                description: error.response?.data?.message || "Failed to fetch usage data"
            });
        } finally {
            setLoadingUsage(false);
        }
    };

    useEffect(() => {
        const fetchOrderDetails = async () => {
            try {
                setLoading(true);
                const response = await api.get(`/api/orders/${id}`);
                setOrder(response.data);
                
                // If it's a Mobimatter order, fetch usage data
                if (response.data.plan?.provider?.name === 'Mobimatter') {
                    fetchUsageData();
                }
            } catch (error) {
                console.error('Error fetching order details:', error);
                toast({
                    variant: "destructive",
                    title: "Error",
                    description: error.response?.data?.message || "Failed to fetch order details"
                });
            } finally {
                setLoading(false);
            }
        };

        if (id) {
            fetchOrderDetails();
        }
    }, [id, toast]);

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
                <div className="container mx-auto p-6">
                    <div className="flex items-center justify-center min-h-[60vh]">
                        <div className="text-center">
                            <div className="relative">
                                <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                                    <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                                </div>
                                <div className="absolute inset-0 w-16 h-16 mx-auto bg-blue-200 rounded-full animate-ping opacity-20"></div>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-700 mb-2">Loading Order Details</h3>
                            <p className="text-gray-500">Please wait while we fetch your order information...</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (!order) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
                <div className="container mx-auto p-6">
                    <div className="flex items-center justify-center min-h-[60vh]">
                        <div className="text-center">
                            <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
                                <Package className="h-8 w-8 text-red-600" />
                            </div>
                            <h2 className="text-2xl font-bold text-gray-800 mb-2">Order Not Found</h2>
                            <p className="text-gray-600 mb-6">The order you're looking for doesn't exist or has been removed.</p>
                            <Button
                                onClick={() => navigate('/dashboard/orders')}
                                className="bg-blue-600 hover:bg-blue-700"
                            >
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Orders
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    const formatBytes = (bytes) => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
            <div className="container mx-auto p-6">
                {/* Header Section */}
                <div className="flex items-center justify-between mb-8">
                    <div className="flex items-center gap-4">
                        <Button
                            variant="outline"
                            onClick={() => navigate('/dashboard/orders')}
                            className="flex items-center gap-2 bg-black text-white border-gray-200 shadow-sm"
                        >
                            <ArrowLeft className="h-4 w-4" />
                            Back to Orders
                        </Button>
                        <div className="hidden md:block">
                            <h1 className="text-2xl font-bold text-gray-900">Order Details</h1>
                            <p className="text-gray-600">View and manage your eSIM order information</p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Badge className={`px-3 py-1 text-sm font-medium border ${getStatusColor(order.status)}`}>
                            {order.status === 'completed' && <CheckCircle className="h-3 w-3 mr-1" />}
                            {order.status === 'pending' && <Clock className="h-3 w-3 mr-1" />}
                            <span className="capitalize">{order.status}</span>
                        </Badge>
                    </div>
                </div>

                {/* Main Content */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Order Summary Card */}
                    <div className="lg:col-span-2">
                        <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                            <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-white/20 rounded-lg">
                                        <Hash className="h-5 w-5" />
                                    </div>
                                    <div>
                                        <CardTitle className="text-xl">Order #{order.id}</CardTitle>
                                        <p className="text-blue-100 text-sm">
                                            Placed on {format(new Date(order.createdAt), 'MMMM d, yyyy h:mm a')}
                                        </p>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="p-6 space-y-6">
                                {/* Order Information Grid */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="space-y-4">
                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <Hash className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">Order Id</h3>
                                                <p className="mt-1 font-semibold text-gray-900">{order.id}</p>
                                            </div>
                                        </div>

                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <Package className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">Plan Name</h3>
                                                <p className="mt-1 font-semibold text-gray-900">{order.plan.name}</p>
                                            </div>
                                        </div>

                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <CreditCard className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">Order Total</h3>
                                                <p className="mt-1 font-semibold text-gray-900 text-lg">${order.orderTotal}</p>
                                            </div>
                                        </div>

                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <CirclePlus  className="h-5 w-5 text-purple-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">Quantity</h3>
                                                <p className="mt-1 font-semibold text-gray-900">{order.quantity}</p>
                                            </div>
                                        </div>

                                        {order.startDate && (
                                            <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                                <Calendar className="h-5 w-5 text-blue-600 mt-0.5" />
                                                <div className="flex-1">
                                                    <h3 className="text-sm font-medium text-gray-700">Start Date</h3>
                                                    <p className="mt-1 font-semibold text-gray-900">{format(new Date(order.startDate), 'MMMM d, yyyy')}</p>
                                                </div>
                                            </div>
                                        )}

                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">Activation Policy</h3>
                                                <p className="mt-1 font-semibold text-gray-900">{order.plan.activationPolicy}</p>
                                            </div>
                                        </div>

                                        {order.stock.walletAuthTransactionId && (
                                            <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                                <Hash className="h-5 w-5 text-blue-600 mt-0.5" />
                                                <div className="flex-1">
                                                    <h3 className="text-sm font-medium text-gray-700">Wallet Auth Transaction ID</h3>
                                                    <div className="flex items-center gap-2 mt-1">
                                                        <p className="font-semibold text-gray-900 truncate">{order.stock.walletAuthTransactionId}</p>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(order.stock.walletAuthTransactionId, 'Transaction ID')}
                                                            className="h-6 w-6 p-0 bg-blue-600 "
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    <div className="space-y-4">
                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <Smartphone className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">ICC ID</h3>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <p className="font-mono text-sm text-gray-900">{order.stock.iccid || 'N/A'}</p>
                                                    {order.stock.iccid && (
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(order.stock.iccid, 'ICC ID')}
                                                            className="h-6 w-6 p-0 bg-blue-600 "
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <Globe className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">SMDP Address</h3>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <p className="font-mono text-sm text-gray-900">{order.stock.smdpAddress || 'N/A'}</p>
                                                    {order.stock.smdpAddress && (
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(order.stock.smdpAddress, 'SMDP Address')}
                                                            className="h-6 w-6 p-0 bg-blue-600 "
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <Wifi className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">Local Profile Assistant (LPA)</h3>
                                                <div className="flex items-start gap-2 mt-1">
                                                    <p className="font-mono text-xs text-gray-900 break-all flex-1">{order.stock.lpaString || 'N/A'}</p>
                                                    {order.stock.lpaString && (
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(order.stock.lpaString, 'LPA String')}
                                                            className="h-6 w-6 p-0 bg-blue-600  flex-shrink-0"
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    )}
                                                </div>
                                            </div>
                                        </div>



                                        <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                            <Globe className="h-5 w-5 text-blue-600 mt-0.5" />
                                            <div className="flex-1">
                                                <h3 className="text-sm font-medium text-gray-700">Access Point Name</h3>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <p className="font-mono text-sm text-gray-900">{order.stock.accessPointName || 'N/A'}</p>
                                                    {order.stock.accessPointName && (
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(order.stock.accessPointName, 'APN')}
                                                            className="h-6 w-6 p-0 bg-blue-600 "
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        {order.stock.phoneNumber && order.stock.phoneNumber !== 'N/A' && (
                                            <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                                <Phone className="h-5 w-5 text-blue-600 mt-0.5" />
                                                <div className="flex-1">
                                                    <h3 className="text-sm font-medium text-gray-700">Phone Number</h3>
                                                    <div className="flex items-center gap-2 mt-1">
                                                        <p className="font-mono text-sm text-gray-900">{order.stock.phoneNumber}</p>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(order.stock.phoneNumber, 'Phone Number')}
                                                            className="h-6 w-6 p-0 bg-blue-600 "
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {order.stock.activationCode && (
                                            <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                                <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
                                                <div className="flex-1">
                                                    <h3 className="text-sm font-medium text-gray-700">Activation Code</h3>
                                                    <div className="flex items-center gap-2 mt-1">
                                                        <p className="font-mono text-sm text-gray-900">{order.stock.activationCode}</p>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(order.stock.activationCode, 'Activation Code')}
                                                            className="h-6 w-6 p-0 bg-blue-600 "
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {order.stock.confCode && (
                                            <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                                                <Hash className="h-5 w-5 text-blue-600 mt-0.5" />
                                                <div className="flex-1">
                                                    <h3 className="text-sm font-medium text-gray-700">Confirmation Code</h3>
                                                    <div className="flex items-center gap-2 mt-1">
                                                        <p className="font-mono text-sm text-gray-900">{order.stock.confCode}</p>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(order.stock.confCode, 'Confirmation Code')}
                                                            className="h-6 w-6 p-0 bg-blue-600 "
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="lg:col-span-1 space-y-6">
                        {/* QR Code Card */}
                        {order.stock.qrCodeUrl && (
                            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                                <CardHeader className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-t-lg">
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 bg-white/20 rounded-lg">
                                            <QrCode className="h-5 w-5" />
                                        </div>
                                        <div>
                                            <CardTitle className="text-lg">eSIM QR Code</CardTitle>
                                            <p className="text-indigo-100 text-sm">Scan to activate your eSIM</p>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="p-6">
                                    <div className="text-center space-y-4">
                                        <div className="bg-white p-4 rounded-xl shadow-inner border-2 border-gray-100">
                                            <img
                                                src={order.stock.qrCodeUrl}
                                                alt="eSIM QR Code"
                                                className="w-full max-w-[200px] mx-auto"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Button
                                                onClick={() => downloadQRCode(order.stock.qrCodeUrl, order.id)}
                                                className="w-full bg-indigo-600 hover:bg-indigo-700 text-white"
                                            >
                                                <Download className="h-4 w-4 mr-2" />
                                                Download QR Code
                                            </Button>
                                        </div>

                                        {/* <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                            <div className="flex items-center gap-2 text-blue-800 text-sm font-medium mb-1">
                                                <Shield className="h-4 w-4" />
                                                Quick Tip
                                            </div>
                                            <p className="text-blue-700 text-xs">
                                                Connect to WiFi before scanning the QR code for best results
                                            </p>
                                        </div> */}
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        {/* Order Status Card */}
                        <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                            <CardHeader className="bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-t-lg">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-white/20 rounded-lg">
                                        <CheckCircle className="h-5 w-5" />
                                    </div>
                                    <div>
                                        <CardTitle className="text-lg">Order Status</CardTitle>
                                        <p className="text-green-100 text-sm">Current status information</p>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="p-6">
                                <div className="space-y-4">
                                    <div className="text-center">
                                        <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium border ${getStatusColor(order.status)}`}>
                                            {order.status === 'completed' && <CheckCircle className="h-4 w-4 mr-2" />}
                                            {order.status === 'pending' && <Clock className="h-4 w-4 mr-2" />}
                                            <span className="capitalize">{order.status}</span>
                                        </div>
                                    </div>

                                    <Separator />

                                    {/* <div className="space-y-3 text-sm">
                                        <div className="flex items-center gap-2 text-gray-600">
                                            <Calendar className="h-4 w-4" />
                                            <span>Placed on {format(new Date(order.createdAt), 'MMM d, yyyy')}</span>
                                        </div>
                                        <div className="flex items-center gap-2 text-gray-600">
                                            <Clock className="h-4 w-4" />
                                            <span>at {format(new Date(order.createdAt), 'h:mm a')}</span>
                                        </div>
                                    </div> */}

                                    {order.status === 'completed' && (
                                        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                                            <div className="flex items-center gap-2 text-green-800 text-sm font-medium">
                                                <CheckCircle className="h-4 w-4" />
                                                Order Completed
                                            </div>
                                            <p className="text-green-700 text-xs mt-1">
                                                Your eSIM is ready to use. Follow the activation instructions below.
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Usage Data Section */}
                <div className="mt-8">
                    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                        <CardHeader className="bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-t-lg">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-white/20 rounded-lg">
                                        <Wifi className="h-5 w-5" />
                                    </div>
                                    <div>
                                        <CardTitle className="text-lg">Usage Data</CardTitle>
                                        <p className="text-purple-100 text-sm">Real-time data consumption tracking</p>
                                    </div>
                                </div>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={fetchUsageData}
                                    disabled={loadingUsage}
                                    className="bg-white/20 border-white/30 text-white hover:bg-white/30"
                                >
                                    {loadingUsage ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                    ) : (
                                        <RefreshCw className="h-4 w-4" />
                                    )}
                                    <span className="ml-2">Refresh</span>
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent className="p-6">
                            {loadingUsage ? (
                                <div className="flex items-center justify-center py-12">
                                    <div className="text-center">
                                        <div className="w-12 h-12 mx-auto mb-4 bg-purple-100 rounded-full flex items-center justify-center">
                                            <Loader2 className="h-6 w-6 animate-spin text-purple-600" />
                                        </div>
                                        <p className="text-gray-600">Loading usage data...</p>
                                    </div>
                                </div>
                            ) : usageData ? (
                                <div className="space-y-6">
                                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
                                        <Table>
                                            <TableHeader>
                                                <TableRow className="border-gray-200">
                                                    <TableHead className="font-semibold text-gray-700">Type</TableHead>
                                                    <TableHead className="font-semibold text-gray-700">Used</TableHead>
                                                    <TableHead className="font-semibold text-gray-700">Data Left</TableHead>
                                                    <TableHead className="font-semibold text-gray-700">Total</TableHead>
                                                    <TableHead className="font-semibold text-gray-700">Status</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                <TableRow className="border-gray-200">
                                                    <TableCell className="font-medium">
                                                        <div className="flex items-center gap-2">
                                                            <Wifi className="h-4 w-4 text-blue-600" />
                                                            Data
                                                        </div>
                                                    </TableCell>
                                                    <TableCell className="font-mono text-sm">
                                                        {usageData.dataUsage ? formatBytes(usageData.dataUsage) : 'N/A'}
                                                    </TableCell>
                                                    <TableCell className="font-mono text-sm">
                                                        {usageData.dataUsage && usageData.dataAllowance ?
                                                            formatBytes(Math.max(0, usageData.dataAllowance - usageData.dataUsage))
                                                            : 'N/A'
                                                        }
                                                    </TableCell>
                                                    <TableCell className="font-mono text-sm">
                                                        {usageData.dataAllowance ? formatBytes(usageData.dataAllowance) : 'N/A'}
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge className={`${getStatusColor(usageData.status)} border`}>
                                                            {usageData.status || 'N/A'}
                                                        </Badge>
                                                    </TableCell>
                                                </TableRow>
                                            </TableBody>
                                        </Table>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        {usageData.activationDate && (
                                            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                                <div className="flex items-center gap-2 text-green-800 font-medium mb-1">
                                                    <CheckCircle className="h-4 w-4" />
                                                    Activation Date
                                                </div>
                                                <p className="text-green-700 text-sm">{format(new Date(usageData.activationDate), 'MMM d, yyyy h:mm a')}</p>
                                            </div>
                                        )}
                                        {usageData.expiryDate && (
                                            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                                                <div className="flex items-center gap-2 text-orange-800 font-medium mb-1">
                                                    <Clock className="h-4 w-4" />
                                                    Expiry Date
                                                </div>
                                                <p className="text-orange-700 text-sm">{format(new Date(usageData.expiryDate), 'MMM d, yyyy h:mm a')}</p>
                                            </div>
                                        )}
                                        {usageData.lastUpdateTime && (
                                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                                <div className="flex items-center gap-2 text-blue-800 font-medium mb-1">
                                                    <RefreshCw className="h-4 w-4" />
                                                    Last Updated
                                                </div>
                                                <p className="text-blue-700 text-sm">{format(new Date(usageData.lastUpdateTime), 'MMM d, yyyy h:mm a')}</p>
                                            </div>
                                        )}
                                    </div>

                                    {usageData.message && (
                                        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                            <p className="text-gray-700 italic text-sm">{usageData.message}</p>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                                        <Wifi className="h-8 w-8 text-gray-400" />
                                    </div>
                                    <h3 className="text-lg font-medium text-gray-700 mb-2">No Usage Data Available</h3>
                                    <p className="text-gray-500 text-sm">
                                        Data usage tracking is not available for this plan at the moment.
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Instructions Section */}
                <div className="mt-8">
                    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                        <CardHeader className="bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-t-lg">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-white/20 rounded-lg">
                                    <Smartphone className="h-5 w-5" />
                                </div>
                                <div>
                                    <CardTitle className="text-xl">eSIM Activation Instructions</CardTitle>
                                    <p className="text-indigo-100 text-sm">Step-by-step guide to activate your eSIM</p>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="p-8">
                            <div className="max-w-4xl mx-auto space-y-8">
                                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                                    <div className="flex items-center gap-2 text-yellow-800 font-medium mb-2">
                                        <Shield className="h-5 w-5" />
                                        Important Notice
                                    </div>
                                    <p className="text-yellow-700 text-sm">
                                        Ensure your phone is connected to a stable WiFi network before scanning the QR code.
                                    </p>
                                </div>

                                <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                                    <div className="flex items-center gap-3 mb-4">
                                        <div className="p-2 bg-blue-100 rounded-lg">
                                            <Smartphone className="h-5 w-5 text-blue-600" />
                                        </div>
                                        <h3 className="text-lg font-semibold text-gray-800">For iPhone (iOS)</h3>
                                    </div>
                                    <ol className="space-y-2 text-gray-700">
                                        <li className="flex items-start gap-3">
                                            <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white text-sm rounded-full flex items-center justify-center font-medium">1</span>
                                            <span>Open <strong>Settings</strong></span>
                                        </li>
                                        <li className="flex items-start gap-3">
                                            <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white text-sm rounded-full flex items-center justify-center font-medium">2</span>
                                            <span>Tap <strong>Mobile Services</strong> → <strong>Add eSIM</strong></span>
                                        </li>
                                        <li className="flex items-start gap-3">
                                            <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white text-sm rounded-full flex items-center justify-center font-medium">3</span>
                                            <span>Scan the QR code on the right</span>
                                        </li>
                                        <li className="flex items-start gap-3">
                                            <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white text-sm rounded-full flex items-center justify-center font-medium">4</span>
                                            <span>Enter the <strong>PIN Code</strong> (if required)</span>
                                        </li>
                                        <li className="flex items-start gap-3">
                                            <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white text-sm rounded-full flex items-center justify-center font-medium">5</span>
                                            <span>Tap <strong>Done</strong></span>
                                        </li>
                                    </ol>
                                    <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                        <p className="text-blue-800 text-sm">
                                            <strong>Alternative:</strong> You can manually enter the details by selecting <strong>&quot;Enter Details Manually&quot;</strong>.
                                        </p>
                                    </div>
                                </div>

                                <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                                    <div className="flex items-center gap-3 mb-4">
                                        <div className="p-2 bg-green-100 rounded-lg">
                                            <Smartphone className="h-5 w-5 text-green-600" />
                                        </div>
                                        <h3 className="text-lg font-semibold text-gray-800">For Android & Other Devices</h3>
                                    </div>
                                    <ol className="space-y-2 text-gray-700">
                                        <li className="flex items-start gap-3">
                                            <span className="flex-shrink-0 w-6 h-6 bg-green-600 text-white text-sm rounded-full flex items-center justify-center font-medium">1</span>
                                            <span>Go to <strong>Settings</strong> → <strong>Cellular/Mobile Network</strong></span>
                                        </li>
                                        <li className="flex items-start gap-3">
                                            <span className="flex-shrink-0 w-6 h-6 bg-green-600 text-white text-sm rounded-full flex items-center justify-center font-medium">2</span>
                                            <span>Tap <strong>Add Cellular Plan / Add Mobile Network</strong></span>
                                        </li>
                                        <li className="flex items-start gap-3">
                                            <span className="flex-shrink-0 w-6 h-6 bg-green-600 text-white text-sm rounded-full flex items-center justify-center font-medium">3</span>
                                            <span>If the APN is not automatically recognized, manually add it under <strong>APN Settings</strong></span>
                                        </li>
                                        <li className="flex items-start gap-3">
                                            <span className="flex-shrink-0 w-6 h-6 bg-green-600 text-white text-sm rounded-full flex items-center justify-center font-medium">4</span>
                                            <span>Assign <strong>Cellular/Mobile Data</strong> access to the newly configured eSIM</span>
                                        </li>
                                        <li className="flex items-start gap-3">
                                            <span className="flex-shrink-0 w-6 h-6 bg-green-600 text-white text-sm rounded-full flex items-center justify-center font-medium">5</span>
                                            <span>Enable <strong>Data Roaming</strong></span>
                                        </li>
                                        <li className="flex items-start gap-3">
                                            <span className="flex-shrink-0 w-6 h-6 bg-green-600 text-white text-sm rounded-full flex items-center justify-center font-medium">6</span>
                                            <span>Disconnect from WiFi and wait a few minutes for the eSIM to activate</span>
                                        </li>
                                        <li className="flex items-start gap-3">
                                            <span className="flex-shrink-0 w-6 h-6 bg-green-600 text-white text-sm rounded-full flex items-center justify-center font-medium">7</span>
                                            <span>If necessary, enter the APN manually under <strong>APN Settings</strong> if the eSIM does not work</span>
                                        </li>
                                    </ol>
                                </div>

                                <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                                    <div className="flex items-center gap-3 mb-4">
                                        <div className="p-2 bg-red-100 rounded-lg">
                                            <Shield className="h-5 w-5 text-red-600" />
                                        </div>
                                        <h3 className="text-lg font-semibold text-red-800">Important Notes</h3>
                                    </div>
                                    <ul className="space-y-3 text-red-700">
                                        <li className="flex items-start gap-3">
                                            <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0"></div>
                                            <span>Your phone must be <strong>carrier-unlocked</strong> and <strong>eSIM-compatible</strong></span>
                                        </li>
                                        <li className="flex items-start gap-3">
                                            <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0"></div>
                                            <span>
                                                <strong>5G availability</strong> depends on network coverage, device compatibility, and regional settings.
                                                Where 5G is unavailable, the eSIM will provide a <strong>high-speed 4G connection</strong> (subject to network conditions).
                                            </span>
                                        </li>
                                        <li className="flex items-start gap-3">
                                            <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0"></div>
                                            <span><strong>No daily limits or throttling</strong>—enjoy full data speeds</span>
                                        </li>
                                        <li className="flex items-start gap-3">
                                            <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0"></div>
                                            <span>Only works with <strong>eSIM-compatible, unlocked phones and tablets</strong></span>
                                        </li>
                                    </ul>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                                        <div className="flex items-center gap-3 mb-4">
                                            <div className="p-2 bg-purple-100 rounded-lg">
                                                <Wifi className="h-5 w-5 text-purple-600" />
                                            </div>
                                            <h3 className="text-lg font-semibold text-purple-800">Track Data Usage</h3>
                                        </div>
                                        <div className="space-y-3 text-purple-700">
                                            <div className="flex items-start gap-3">
                                                <span className="text-lg">📱</span>
                                                <div>
                                                    <p className="font-medium">For iPhone:</p>
                                                    <p className="text-sm">Go to <strong>Settings</strong> → <strong>Cellular</strong> and scroll down to view usage</p>
                                                </div>
                                            </div>
                                            <div className="flex items-start gap-3">
                                                <span className="text-lg">📱</span>
                                                <div>
                                                    <p className="font-medium">For Android & Other OS:</p>
                                                    <p className="text-sm">Go to <strong>Settings</strong> → <strong>Network & Internet</strong> and check your eSIM details</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
                                        <div className="flex items-center gap-3 mb-4">
                                            <div className="p-2 bg-orange-100 rounded-lg">
                                                <Package className="h-5 w-5 text-orange-600" />
                                            </div>
                                            <h3 className="text-lg font-semibold text-orange-800">Need More Data?</h3>
                                        </div>
                                        <p className="text-orange-700">
                                            If you run out of data, simply <strong>purchase a new package</strong> and repeat the activation steps.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
};

export default OrderDetails;
